// checkDatabase.js - Check actual database structure and data
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USERNAME || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'wolffoxx',
  port: process.env.DB_PORT || 3306
};

const checkDatabase = async () => {
  let connection;
  
  try {
    console.log('🔍 Checking database structure...');
    
    // Create database connection
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connected successfully');
    
    // Check if products table exists and get its structure
    console.log('\n📋 Products table structure:');
    const [columns] = await connection.execute("DESCRIBE products");
    console.table(columns);
    
    // Check existing products
    console.log('\n📦 Existing products:');
    const [products] = await connection.execute("SELECT id, name, category_id FROM products LIMIT 10");
    if (products.length > 0) {
      console.table(products);
    } else {
      console.log('❌ No products found in database');
    }
    
    // Check product_images table
    console.log('\n🖼️  Product images table structure:');
    try {
      const [imageColumns] = await connection.execute("DESCRIBE product_images");
      console.table(imageColumns);
      
      // Check existing images
      console.log('\n📸 Existing product images:');
      const [images] = await connection.execute("SELECT id, product_id, image_url, is_primary FROM product_images LIMIT 5");
      if (images.length > 0) {
        console.table(images);
      } else {
        console.log('❌ No product images found in database');
      }
    } catch (error) {
      console.log('❌ product_images table does not exist');
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
};

// Run the check
checkDatabase();
