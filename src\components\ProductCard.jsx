import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { Eye, ChevronLeft, ChevronRight, ZoomIn, RotateCcw, Star } from 'lucide-react';
import WishlistButton from './WishlistButton';
import CartButton from './CartButton';
import ColorPreviewSlider from './ColorPreviewSlider';

export default function ProductCard({ product, viewMode = 'grid', index = 0, searchColor = null, reviewStats = null }) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedColorIndex, setSelectedColorIndex] = useState(0);
  const touchStartX = useRef(0);
  const touchEndX = useRef(0);

  // Debug product sale status
  useEffect(() => {
    if (product.sale_price && product.sale_price > 0) {
      console.log('🔍 ProductCard: Product with sale price:', {
        name: product.name,
        is_sale: product.is_sale,
        price: product.price,
        sale_price: product.sale_price
      });
      
      // If product has sale_price but is_sale is not 1, log a warning
      if (product.is_sale !== 1) {
        console.warn('⚠️ ProductCard: Product has sale_price but is_sale is not 1:', product.name);
      }
    }
  }, [product]);


  // Auto-select searched color when component loads
  useEffect(() => {
    if (searchColor && product.colors && product.colors.length > 0) {
      const colorIndex = product.colors.findIndex(color =>
        color.name.toLowerCase().includes(searchColor.toLowerCase()) ||
        color.name.toLowerCase() === searchColor.toLowerCase()
      );
      if (colorIndex !== -1) {
        setSelectedColorIndex(colorIndex);
        setCurrentImageIndex(0); // Reset to first image of the selected color
      }
    }
  }, [searchColor, product.colors]);

  // Get current images based on selected color
  const getCurrentImages = () => {
    if (product.colors && product.colors[selectedColorIndex] && product.colors[selectedColorIndex].images) {
      return product.colors[selectedColorIndex].images;
    }
    return product.images || [];
  };

  const currentImages = getCurrentImages();

  const handleColorSelect = (colorIndex) => {
    setSelectedColorIndex(colorIndex);
    // Reset to first image when color changes
    setCurrentImageIndex(0);
  };

  const nextImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex((prev) =>
      prev === currentImages.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = (e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex((prev) =>
      prev === 0 ? currentImages.length - 1 : prev - 1
    );
  };

  const goToImage = (index, e) => {
    e.preventDefault();
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    setCurrentImageIndex(index);
  };

  // Touch handlers for swipe functionality
  const handleTouchStart = (e) => {
    touchStartX.current = e.targetTouches[0].clientX;
  };

  const handleTouchMove = (e) => {
    touchEndX.current = e.targetTouches[0].clientX;
  };

  const handleTouchEnd = (e) => {
    e.preventDefault();
    e.stopPropagation();

    if (!touchStartX.current || !touchEndX.current) return;

    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe && currentImages.length > 1) {
      setCurrentImageIndex((prev) => (prev + 1) % currentImages.length);
    }
    if (isRightSwipe && currentImages.length > 1) {
      setCurrentImageIndex((prev) => (prev === 0 ? currentImages.length - 1 : prev - 1));
    }
  };

  if (viewMode === 'list') {
    return (
      <motion.div
        className="group bg-[#0a0a0a] backdrop-blur-xl rounded-2xl overflow-hidden border border-[#404040] hover:border-[#6a6a6a] transition-all duration-300 shadow-xl"
        whileHover={{ y: -4 }}
        onHoverStart={() => setIsHovered(true)}
        onHoverEnd={() => setIsHovered(false)}
      >
        <div className="flex flex-col sm:flex-row">
          {/* Image Section */}
          <div className="relative w-full sm:w-64 h-64 sm:h-48 overflow-hidden">
            <Link to={`/product/${product.id}`} className="block h-full">
              <ColorPreviewSlider product={product} className="h-full" />

              {/* Badges */}
              <div className="absolute top-2 left-2 flex flex-col gap-1">
                {product.isNew && (
                  <div className="bg-[#3b82f6] text-white text-[10px] font-medium px-1.5 py-0.5 rounded-md shadow-lg">
                    NEW
                  </div>
                )}
                {/* Show SALE badge if is_sale is 1 or if there's a valid sale_price lower than regular price */}
                {((product.is_sale === 1 || product.is_sale === '1') || (product.sale_price && product.sale_price < product.price)) && (
                  <div className="bg-red-500 text-white text-[10px] font-medium px-1.5 py-0.5 rounded-md shadow-lg">
                    SALE
                  </div>
                )}
              </div>

              {/* Wishlist Button */}
              <div className="absolute top-3 right-3">
                <WishlistButton
                  productId={product.id}
                  productName={product.name}
                  productPrice={((product.is_sale === 1 || product.is_sale === '1') && product.sale_price && product.sale_price < product.price) 
                    ? product.sale_price 
                    : product.price}
                  productImage={product.images[0]}
                  className="bg-[#2a2a2a] hover:bg-[#404040] backdrop-blur-sm w-8 h-8 rounded-full flex items-center justify-center transition-all"
                />
              </div>
            </Link>
          </div>

          {/* Content Section */}
          <div className="flex-1 p-6 flex flex-col justify-between">
            <div>
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="text-white font-semibold text-xl hover:text-cyan-400 transition-colors line-clamp-1 leading-tight truncate">
                    <Link to={`/product/${product.id}`} className="block truncate" title={product.name}>{product.name}</Link>
                  </h3>
                </div>

                {/* Rating */}
                {((reviewStats && reviewStats.average_rating > 0) || (product.average_rating && product.average_rating > 0)) && (
                  <div className="flex items-center gap-1 ml-4">
                    <Star size={14} className="text-yellow-400 fill-current" />
                    <span className="text-slate-300 text-sm font-medium">
                      {reviewStats
                        ? reviewStats.average_rating.toFixed(1)
                        : parseFloat(product.average_rating).toFixed(1)
                      }
                    </span>
                    {((reviewStats && reviewStats.total_reviews) || product.total_reviews) && (
                      <span className="text-slate-400 text-xs">
                        ({reviewStats ? reviewStats.total_reviews : product.total_reviews})
                      </span>
                    )}
                  </div>
                )}
              </div>

              <p className="text-slate-400 text-sm line-clamp-2 mb-4">
                {product.description || 'Premium quality streetwear designed for comfort and style.'}
              </p>

              {/* Colors */}
              {product.colors && product.colors.length > 0 && (
                <div className="flex items-center gap-2 mb-4">
                  <div className="flex gap-1">
                    {product.colors.slice(0, 4).map((color, idx) => (
                      <div
                        key={idx}
                        className="w-4 h-4 rounded-full border border-[#404040]"
                        style={{ backgroundColor: color.value }}
                        title={color.name}
                      />
                    ))}
                    {product.colors.length > 4 && (
                      <div className="w-4 h-4 rounded-full bg-[#2a2a2a] flex items-center justify-center text-xs text-[#9a9a9a] font-medium">
                        +{product.colors.length - 4}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Bottom Section */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {/* Check both is_sale flag and if sale_price exists and is lower than regular price */}
                {((product.is_sale === 1 || product.is_sale === '1') && product.sale_price && product.sale_price < product.price) ? (
                  <>
                    <span className="text-white font-bold text-xl">${parseFloat(product.sale_price).toFixed(2)}</span>
                    <span className="text-slate-400 line-through text-sm">${parseFloat(product.price).toFixed(2)}</span>
                    <span className="text-xs bg-red-500/20 text-red-400 px-2 py-1 rounded-full font-medium">
                      {Math.round(((parseFloat(product.price) - parseFloat(product.sale_price)) / parseFloat(product.price)) * 100)}% OFF
                    </span>
                  </>
                ) : (
                  <span className="text-white font-bold text-xl">${parseFloat(product.price).toFixed(2)}</span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                <Link
                  to={`/product/${product.id}`}
                  className="bg-[#2a2a2a] hover:bg-[#404040] text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-all duration-200 text-sm font-medium"
                >
                  <Eye size={14} />
                  View
                </Link>
                <CartButton 
                  product={product}
                  selectedColor={product.colors && product.colors[selectedColorIndex]}
                  selectedSize={product.sizes && product.sizes[0]}
                  className="bg-gradient-to-br from-[#FF6B35] to-[#F7931E] hover:from-[#e55a2b] hover:to-[#e0841a] text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors text-sm font-medium"
                  buttonText="Add to Cart"
                />
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  // Grid View (Default) - Using FeaturedCollections Template
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      viewport={{ once: true, margin: "-50px" }}
      className="bg-[#0a0a0a] rounded-xl overflow-hidden group border border-[#404040]"
    >
      <Link to={`/product/${product.id}`} className="block relative">
        {/* Image Container with Touch Swipe */}
        <div
          className="h-64 sm:h-72 md:h-80 lg:h-88 overflow-hidden relative"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        >
          <div className="relative w-full h-full">
            {currentImages.map((image, idx) => (
              <img
                key={idx}
                src={image}
                alt={`${product.name} - ${product.colors[selectedColorIndex]?.name || 'Default'} - Image ${idx + 1}`}
                loading="lazy"
                className={`absolute inset-0 w-full h-full object-cover transform group-hover:scale-105 transition-all duration-700 ease-in-out ${
                  idx === currentImageIndex
                    ? 'opacity-100 z-10'
                    : 'opacity-0 z-0'
                }`}
                onError={(e) => {
                  e.target.src = '/api/placeholder/400/400';
                }}
              />
            ))}
          </div>

          {/* Desktop Navigation Arrows - Always visible on desktop */}
          {currentImages.length > 1 && (
            <>
              <button
                onClick={prevImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="hidden md:flex absolute left-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-[#2a2a2a] hover:bg-[#404040] text-white rounded-full items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronLeft size={16} />
              </button>
              <button
                onClick={nextImage}
                onMouseDown={(e) => e.stopPropagation()}
                onTouchStart={(e) => e.stopPropagation()}
                className="hidden md:flex absolute right-2 top-1/2 -translate-y-1/2 w-8 h-8 bg-[#2a2a2a] hover:bg-[#404040] text-white rounded-full items-center justify-center transition-all duration-200 backdrop-blur-sm z-20 opacity-100"
              >
                <ChevronRight size={16} />
              </button>
            </>
          )}

          {/* Image Dots Indicator */}
          {currentImages.length > 1 && (
            <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex gap-1.5 z-20">
              {currentImages.map((_, idx) => (
                <button
                  key={idx}
                  onClick={(e) => goToImage(idx, e)}
                  onMouseDown={(e) => e.stopPropagation()}
                  onTouchStart={(e) => e.stopPropagation()}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    idx === currentImageIndex
                      ? 'bg-white scale-110'
                      : 'bg-white/50 hover:bg-white/75'
                  }`}
                />
              ))}
            </div>
          )}

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        </div>

        {/* Professional Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
          {product.isNew && (
            <div className="bg-[#3b82f6] text-white text-[10px] font-medium px-1.5 py-0.5 rounded-md shadow-lg">
              NEW
            </div>
          )}
          {/* Show SALE badge if is_sale is 1 or if there's a valid sale_price lower than regular price */}
          {((product.is_sale === 1 || product.is_sale === '1') || (product.sale_price && product.sale_price < product.price)) && (
            <div className="bg-red-500 text-white text-[10px] font-medium px-1.5 py-0.5 rounded-md shadow-lg">
              SALE
            </div>
          )}
          {product.isLimitedEdition && (
            <div className="bg-orange-500 text-white text-[10px] font-medium px-1.5 py-0.5 rounded-md shadow-lg">
              LIMITED
            </div>
          )}
        </div>

        {/* Image Counter */}
        {currentImages.length > 1 && (
          <div className="absolute top-3 right-11 bg-black/50 text-white text-xs px-1.5 py-0.5 rounded-full backdrop-blur-sm">
            {currentImageIndex + 1}/{currentImages.length}
          </div>
        )}

        {/* Wishlist Button */}
        <div className="absolute top-3 right-3 z-10">
          <WishlistButton
            productId={product.id}
            productName={product.name}
            productPrice={((product.is_sale === 1 || product.is_sale === '1') && product.sale_price && product.sale_price < product.price) 
              ? product.sale_price 
              : product.price}
            productImage={currentImages[0] || product.images?.[0]}
            className="bg-[#2a2a2a] hover:bg-[#404040] text-white w-6 h-6 rounded-full flex items-center justify-center transition-all duration-200"
          />
        </div>

        {/* Quick Actions Overlay */}
        {/* <AnimatedInfo product={product} isVisible={isHovered} /> */}
      </Link>

      {/* Product Info */}
      <div className="p-2.5 sm:p-3 md:p-3">
        <div className="mb-2">
          <h3 className="text-[#e2e8f0] font-semibold text-sm sm:text-base md:text-base hover:text-[#3b82f6] transition-colors line-clamp-1 leading-tight truncate">
            <Link to={`/product/${product.id}`} className="block truncate" title={product.name}>{product.name}</Link>
          </h3>
        </div>

        
        {/* Price */}
        <div className="flex items-center gap-1.5 mb-2">
          {/* Check both is_sale flag and if sale_price exists and is lower than regular price */}
          {((product.is_sale === 1 || product.is_sale === '1') && product.sale_price && product.sale_price < product.price) ? (
            <>
              <span className="text-white font-semibold text-sm sm:text-base">₹{parseFloat(product.sale_price).toFixed(2)}</span>
              <span className="text-slate-400 line-through text-xs">₹{parseFloat(product.price).toFixed(2)}</span>
            </>
          ) : (
            <span className="text-white font-semibold text-sm sm:text-base">₹{parseFloat(product.price).toFixed(2)}</span>
          )}
        </div>

        {/* Color Selection */}
        <div className="flex items-center gap-1.5">
          <div className="flex gap-1">
            {product.colors.slice(0, 4).map((color, idx) => {
              const isSearchedColor = searchColor &&
                (color.name.toLowerCase().includes(searchColor.toLowerCase()) ||
                 color.name.toLowerCase() === searchColor.toLowerCase());

              return (
                <button
                  key={idx}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    handleColorSelect(idx);
                  }}
                  className={`w-4 h-4 rounded-full border transition-all duration-200 relative ${
                    selectedColorIndex === idx
                      ? 'border-white scale-110 shadow-lg shadow-white/25'
                      : 'border-[#404040] hover:border-[#6a6a6a]'
                  } ${
                    isSearchedColor ? 'ring-2 ring-blue-400 ring-offset-1 ring-offset-[#0a0a0a]' : ''
                  }`}
                  style={{ backgroundColor: color.value }}
                  title={`${color.name}${isSearchedColor ? ' (Searched Color)' : ''}`}
                >
                  {isSearchedColor && (
                    <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-400 rounded-full border border-[#0a0a0a]"></div>
                  )}
                </button>
              );
            })}
            {product.colors.length > 4 && (
              <div className="w-4 h-4 rounded-full bg-[#2a2a2a] border border-[#404040] flex items-center justify-center">
                <span className="text-[#9a9a9a] text-xs font-medium">+{product.colors.length - 4}</span>
              </div>
            )}
          </div>
        </div>

        {/* <div className="flex gap-2 mt-4">
          <Link
            to={`/product/${product.id}`}
            className="bg-white/10 hover:bg-white/20 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-all duration-200 backdrop-blur-sm border border-white/20"
          >
            <Eye size={12} />
            Quick View
          </Link>
          <CartButton 
            product={product}
            selectedColor={product.colors && product.colors[selectedColorIndex]}
            selectedSize={product.sizes && product.sizes[0]}
            className="bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-colors"
            buttonText="Add to Cart"
            showIcon={true}
          />
        </div> */}
      </div>
    </motion.div>
  );
}

// AnimatedInfo component for quick actions overlay
// function AnimatedInfo({ product, isVisible }) {
//   return (
//     <motion.div
//       initial={{ opacity: 0, y: 20 }}
//       animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
//       transition={{ duration: 0.3 }}
//       className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/90 via-black/60 to-transparent p-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
//     >
//       <div className="flex gap-2">
//         <Link
//           to={`/product/${product.id}`}
//           className="bg-white/10 hover:bg-white/20 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-all duration-200 backdrop-blur-sm border border-white/20"
//         >
//           <Eye size={12} />
//           Quick View
//         </Link>
//         <CartButton 
//           product={product}
//           selectedColor={product.colors && product.colors[selectedColorIndex]}
//           selectedSize={product.sizes && product.sizes[0]}
//           className="bg-indigo-600 hover:bg-indigo-700 text-white text-xs font-medium px-3 py-2 rounded-lg flex items-center gap-1.5 transition-colors"
//           buttonText="Add to Cart"
//           showIcon={true}
//         />
//       </div>
//     </motion.div>
//   );
// }